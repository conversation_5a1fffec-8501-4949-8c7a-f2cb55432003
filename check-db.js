const Database = require('better-sqlite3');

try {
  const db = new Database('data.db');

  console.log('Checking database tables...');

  // Get all tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log('Tables:', tables.map(t => t.name));

  // Check each table for data
  for (const table of tables) {
    try {
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
      console.log(`${table.name}: ${count.count} records`);

      // Show sample data for smaller tables
      if (count.count > 0 && count.count <= 10) {
        const sample = db.prepare(`SELECT * FROM ${table.name} LIMIT 3`).all();
        console.log(`Sample data from ${table.name}:`, sample);
      }
    } catch (error) {
      console.log(`Error checking ${table.name}:`, error.message);
    }
  }

  db.close();
} catch (error) {
  console.error('Database error:', error);
}
