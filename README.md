# PayPal Invoice Generator & E-commerce Platform

A comprehensive full-stack e-commerce and invoice management platform built with React, TypeScript, Express.js, and SQLite. This application provides a complete solution for managing products, processing payments, generating invoices, and handling customer communications.

## 🚀 Features

### Core Functionality
- **Product Management**: Create, edit, and manage products with images, descriptions, and pricing
- **Invoice Generation**: Automated PayPal invoice creation and management
- **Payment Processing**: Multiple payment providers including PayPal, custom payment links, and embedded buttons
- **Custom Checkout Pages**: Create branded checkout experiences with custom URLs
- **Email Management**: Automated email notifications with customizable templates
- **Order Management**: Track orders, trial orders, and customer information

### Admin Dashboard
- **Analytics Dashboard**: Revenue tracking, order statistics, and performance metrics
- **Product Management**: Full CRUD operations for products
- **Order Management**: View and manage all orders and invoices
- **Customer Management**: Handle customer data and allowed email domains
- **Payment Settings**: Configure PayPal, custom payment links, and other payment providers
- **Email Settings**: SMTP configuration and email template management
- **System Monitoring**: Real-time system health and performance monitoring

### Advanced Features
- **Trial Orders**: Support for trial subscriptions and upgrades
- **Email Domain Restrictions**: Control which email domains can place orders
- **Telegram Integration**: Automated notifications via Telegram bot
- **Custom Branding**: Customizable themes, logos, and branding
- **SEO & Privacy Controls**: Meta tags, robots.txt, and privacy settings
- **Data Export/Import**: Backup and restore functionality
- **System Messages**: Customizable system-wide messages and notifications

## 🛠 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Wouter** for routing
- **TanStack Query** for data fetching
- **Radix UI** for accessible components
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Vite** for build tooling

### Backend
- **Node.js** with Express.js
- **TypeScript** for type safety
- **SQLite** with Drizzle ORM
- **Express Session** for authentication
- **Multer** for file uploads
- **Nodemailer** for email functionality

### Database Schema
The application uses SQLite with the following main tables:
- `users` - Admin user accounts
- `products` - Product catalog
- `invoices` - Order and invoice records
- `custom_checkout_pages` - Custom branded checkout pages
- `allowed_emails` - Email domain restrictions
- `email_templates` - Customizable email templates
- `paypal_buttons` - PayPal button configurations
- `custom_invoices` - Custom invoice management
- `smtp_providers` - Email provider configurations
- `system_messages` - System-wide messages

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd deploy3
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env` file with the following variables:
   ```env
   DATABASE_URL=sqlite:data.db
   SESSION_SECRET=your-session-secret
   NODE_ENV=development
   SECURE_COOKIES=false
   ADMIN_ACCESS_TOKEN=your-admin-token
   ```

4. **Database Setup**
   The application uses SQLite and will create the database automatically on first run.

5. **Start Development Server**
   ```bash
   # For Windows
   npm run dev:windows
   
   # For Unix/Linux/Mac
   npm run dev
   ```

6. **Access the Application**
   - Frontend: http://localhost:3001
   - Admin Panel: http://localhost:3001/admin/login?token=your-admin-token

## 🔧 Configuration

### Payment Providers
The application supports multiple payment methods:
- **PayPal**: Full PayPal integration with invoice generation
- **Custom Payment Links**: Direct payment URLs
- **PayPal Button Embed**: Embedded PayPal payment buttons

### Email Configuration
Configure SMTP providers for automated email notifications:
- Order confirmations
- Invoice notifications
- System alerts
- Custom email templates

### Admin Access
Admin access is protected by a token-based system. Include the admin token in the URL to access admin features:
```
http://localhost:3001/admin/login?token=your-admin-token
```

## 🚀 Production Deployment

### Build for Production
```bash
npm run build:prod
```

### Start Production Server
```bash
# Windows
npm run start:windows

# Unix/Linux/Mac
npm run start
```

### Production Considerations
- Set `NODE_ENV=production` in environment variables
- Configure proper SSL certificates for HTTPS
- Set up reverse proxy (Nginx recommended)
- Configure secure session secrets
- Set up proper database backups

## 📊 Database Management

### Backup & Restore
The application includes built-in backup functionality:
- Automated backups of all data
- Export/import capabilities
- System restore functionality

### Migrations
Database schema changes are handled through migration scripts:
```bash
npm run db:push
```

## 🔐 Security Features

- **Session-based Authentication**: Secure admin authentication
- **CSRF Protection**: Built-in CSRF protection
- **Email Domain Restrictions**: Control customer access
- **Privacy Controls**: SEO and privacy settings
- **Secure File Uploads**: Protected file upload handling

## 📱 API Endpoints

### Public API
- `GET /api/products` - Get all active products
- `POST /api/checkout` - Process checkout and create invoice
- `GET /api/products/:id` - Get specific product

### Admin API
- `GET /api/admin/stats` - Dashboard statistics
- `POST /api/admin/products` - Create/update products
- `GET /api/admin/orders` - Get all orders
- `POST /api/admin/email/send` - Send custom emails

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the admin dashboard for system monitoring
- Review application logs for debugging
- Use the built-in backup/restore functionality for data recovery

## 🎯 Use Cases

### E-commerce Businesses
- Online product sales with automated invoicing
- Digital product delivery
- Subscription and trial management
- Customer communication automation

### Service Providers
- Invoice generation for services
- Payment link creation
- Customer onboarding
- Automated follow-ups

### Content Creators
- Digital product sales
- Course and content monetization
- Subscription management
- Fan engagement

## 📋 Admin Panel Features

### Dashboard Overview
- Real-time sales analytics
- Order tracking and management
- Revenue reporting
- Customer insights

### Product Management
- Add/edit/delete products
- Image upload and management
- Pricing and description management
- Product activation/deactivation

### Order Management
- View all orders and invoices
- Track payment status
- Customer information management
- Order fulfillment tracking

### Payment Configuration
- PayPal integration setup
- Custom payment link management
- Payment provider rotation
- Transaction monitoring

### Email System
- SMTP provider configuration
- Email template customization
- Automated notification setup
- Email delivery tracking

### System Administration
- User account management
- Security settings
- System monitoring
- Backup and restore
- Data export/import

## 🔄 Workflow Examples

### Standard Purchase Flow
1. Customer visits homepage
2. Browses available products
3. Selects product and proceeds to checkout
4. Fills out customer information
5. Payment is processed via configured provider
6. Invoice is generated and sent via email
7. Order is tracked in admin dashboard
8. Telegram notification sent to admin (if configured)

### Custom Checkout Flow
1. Admin creates custom checkout page
2. Custom URL is generated (e.g., /checkout/special-offer)
3. Customer visits custom checkout page
4. Branded checkout experience with custom messaging
5. Payment processing and invoice generation
6. Custom confirmation message displayed
7. Analytics tracked separately for custom pages

### Trial Order Flow
1. Customer initiates trial order
2. Trial-specific payment processing
3. Trial period tracking
4. Upgrade notifications
5. Conversion to full subscription
6. Automated follow-up communications

## 🛡️ Security & Privacy

### Data Protection
- Secure session management
- Encrypted password storage
- HTTPS enforcement in production
- CSRF protection
- Input validation and sanitization

### Privacy Controls
- Configurable robots.txt
- Meta tag management
- Cookie consent handling
- Data retention policies
- GDPR compliance features

### Access Control
- Token-based admin access
- Session timeout management
- IP-based restrictions (configurable)
- Email domain whitelisting
- Role-based permissions

## 🔧 Customization Options

### Branding
- Custom logos and favicons
- Color scheme customization
- Custom CSS injection
- Branded email templates
- Custom checkout page themes

### Functionality
- Payment provider selection
- Email template modification
- Custom field addition
- Workflow customization
- Integration with external services

### Localization
- Multi-language support structure
- Currency configuration
- Date/time formatting
- Regional payment methods
- Localized email templates

## 📈 Monitoring & Analytics

### Built-in Analytics
- Sales performance tracking
- Conversion rate monitoring
- Customer behavior analysis
- Payment success rates
- Email delivery statistics

### System Monitoring
- Server health monitoring
- Database performance tracking
- Error logging and alerting
- Uptime monitoring
- Resource usage tracking

### Reporting
- Sales reports generation
- Customer data export
- Transaction history
- Performance metrics
- Custom report creation

## 🚨 Troubleshooting

### Common Issues
- **Database Connection**: Check DATABASE_URL in .env file
- **Email Delivery**: Verify SMTP configuration in admin panel
- **PayPal Integration**: Ensure correct API credentials
- **File Uploads**: Check upload directory permissions
- **Session Issues**: Verify SESSION_SECRET configuration

### Debug Mode
Enable debug logging by setting NODE_ENV=development and checking console output for detailed error messages.

### Support Resources
- Admin dashboard system monitoring
- Built-in error logging
- Database backup/restore functionality
- Configuration export/import
- System health checks

---

**Note**: This application is designed for small to medium-scale e-commerce operations with a focus on PayPal integration and automated invoice management. It provides a complete solution for businesses looking to streamline their payment processing and customer communication workflows.
